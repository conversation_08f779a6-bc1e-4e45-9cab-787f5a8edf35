# In TypeScript actions, `dist/` is a special directory. When you reference
# an action with the `uses:` property, `dist/index.js` is the code that will be
# run. For this project, the `dist/index.js` file is transpiled from other
# source files. This workflow ensures the `dist/` directory contains the
# expected transpiled code.
#
# If this workflow is run from a feature branch, it will act as an additional CI
# check and fail if the checked-in `dist/` directory does not match what is
# expected from the build.
name: Check Transpiled JavaScript

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

permissions:
  contents: read

jobs:
  check-dist:
    name: Check dist/
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        id: setup-node
        uses: actions/setup-node@v4
        with:
          node-version-file: .node-version

      - uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install Dependencies
        id: install
        run: pnpm install
        working-directory: eval

      - name: Build dist/ Directory
        id: build
        run: pnpm build
        working-directory: eval

      # This will fail the workflow if the `dist/` directory is different than
      # expected.
      - name: Compare Directories
        id: diff
        run: |
          if [ "$(git diff --ignore-space-at-eol --text dist/ | wc -l)" -gt "0" ]; then
            echo "Detected uncommitted changes after build. See status below:"
            git diff --ignore-space-at-eol --text dist/
            exit 1
          fi
        working-directory: eval

      # If `dist/` was different than expected, upload the expected version as a
      # workflow artifact.
      - if: ${{ failure() && steps.diff.outcome == 'failure' }}
        name: Upload Artifact
        id: upload
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: eval/dist/
