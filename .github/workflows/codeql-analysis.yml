name: CodeQL

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main
  schedule:
    - cron: "31 7 * * 3"

permissions:
  actions: read
  checks: write
  contents: read
  security-events: write

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest

    strategy:
      fail-fast: false
      matrix:
        language:
          - TypeScript

    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        id: initialize
        uses: github/codeql-action/init@v3
        with:
          languages: ${{ matrix.language }}
          source-root: eval/src

      - name: Autobuild
        id: autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        id: analyze
        uses: github/codeql-action/analyze@v3
