name: Run Python evals

on:
  push:
    # files:
    #   - 'test-eval/**'

permissions:
  pull-requests: write
  contents: read

jobs:
  eval:
    name: Run Python evals
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: "recursive"

      - name: Install uv
        uses: astral-sh/setup-uv@v5

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.12" # TODO: Matrix test different versions

      - name: Install dependencies
        run: |
          cd test-eval-py
          uv lock --check
          uv sync --no-dev

      - name: Run Evals
        uses: ./
        with:
          api_key: ${{ secrets.BRAINTRUST_API_KEY }}
          root: test-eval-py
          runtime: python
          package_manager: uv

      # - name: Start terminal session
      #   uses: mxschmitt/action-tmate@v3
      #   with:
      #     limit-access-to-actor: true
