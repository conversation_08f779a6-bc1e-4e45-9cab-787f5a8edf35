name: Lint Codebase

on:
  pull_request:
    branches:
      - main
  push:
    branches:
      - main

permissions:
  contents: read
  packages: read
  statuses: write

jobs:
  lint:
    name: Lint Codebase
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        id: checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        id: setup-node
        uses: actions/setup-node@v4
        with:
          node-version-file: .node-version

      - uses: pnpm/action-setup@v3
        with:
          version: 8

      - name: Get pnpm store directory
        shell: bash
        run: |
          echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

      - uses: actions/cache@v4
        name: Setup pnpm cache
        with:
          path: ${{ env.STORE_PATH }}
          key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
          restore-keys: |
            ${{ runner.os }}-pnpm-store-

      - name: Install Dependencies
        id: install
        run: pnpm install
        working-directory: eval

      - name: Lint Codebase
        id: super-linter
        uses: super-linter/super-linter/slim@v6
        working-directory: eval
        env:
          DEFAULT_BRANCH: main
          FILTER_REGEX_EXCLUDE: dist/**/*
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          TYPESCRIPT_DEFAULT_STYLE: prettier
          VALIDATE_ALL_CODEBASE: true
          VALIDATE_JAVASCRIPT_STANDARD: false
          VALIDATE_JSCPD: false
