{"name": "typescript-action", "description": "GitHub Actions TypeScript template", "version": "0.0.0", "author": "", "private": true, "homepage": "https://github.com/actions/typescript-action", "repository": {"type": "git", "url": "git+https://github.com/actions/typescript-action.git"}, "bugs": {"url": "https://github.com/actions/typescript-action/issues"}, "keywords": ["actions", "node", "setup"], "exports": {".": "./dist/index.js"}, "engines": {"node": ">=20"}, "scripts": {"build": "npm run format:write && npm run package", "ci-test": "npx jest", "coverage": "npx make-coverage-badge --output-path ./badges/coverage.svg", "format:write": "npx prettier --write .", "format:check": "npx prettier --check .", "lint": "npx eslint .", "watch": "npm run package:watch", "package": "esbuild --platform=node --bundle src/index.ts  --outfile=dist/index.js --minify --sourcemap --target=es2020 --loader:.txt=text", "package:watch": "npm run package -- --watch", "test": "npx jest", "all": "npm run format:write && npm run lint && npm run test && npm run coverage && npm run package"}, "license": "MIT", "jest": {"preset": "ts-jest", "verbose": true, "clearMocks": true, "testEnvironment": "node", "moduleFileExtensions": ["js", "ts"], "testMatch": ["**/*.test.ts"], "testPathIgnorePatterns": ["/node_modules/", "/dist/"], "transform": {"^.+\\.ts$": "ts-jest"}, "coverageReporters": ["json-summary", "text", "lcov"], "collectCoverage": true, "collectCoverageFrom": ["./src/**"]}, "dependencies": {"@actions/core": "^1.10.1", "@actions/github": "^6.0.0", "@braintrust/core": "^0.0.34", "braintrust": "^0.0.124", "zod": "^3.23.4"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "@types/node": "^20.12.7", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "esbuild": "^0.25.0", "eslint": "^8.57.0", "eslint-plugin-github": "^4.10.2", "eslint-plugin-jest": "^28.2.0", "eslint-plugin-jsonc": "^2.15.1", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "make-coverage-badge": "^1.2.0", "prettier": "^3.2.5", "prettier-eslint": "^16.3.0", "ts-jest": "^29.1.2", "typescript": "^5.4.5"}}