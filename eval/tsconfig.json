{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"target": "ES2022", "module": "NodeNext", "rootDir": "./src", "moduleResolution": "NodeNext", "baseUrl": "./", "sourceMap": true, "outDir": "./dist", "noImplicitAny": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "skipLibCheck": true, "newLine": "lf"}, "exclude": ["./dist", "./node_modules", "./__tests__", "./coverage", "./test-eval"]}