import { Eval } from "braintrust";

// Simple smoke test eval
Eval("Smoke Test", {
  data: () => [
    { input: "Hello", expected: "Hello" },
    { input: "World", expected: "World" },
  ],
  task: async (input) => {
    // Simple echo task for smoke testing
    return input;
  },
  scores: [
    (args: { input: string; output: string; expected: string }) => ({
      name: "exact_match",
      score: args.output === args.expected ? 1 : 0,
    }),
  ],
});
