name: Reusable Evals Workflow

on:
  workflow_call:
    inputs:
      model_for_eval:
        description: 'Model to use for evaluation'
        required: false
        type: string
        default: 'us.anthropic.claude-sonnet-4-20250514-v1:0'
      eval_paths:
        description: 'Paths to evaluation files'
        required: true
        type: string

jobs:
  run-evals:
    runs-on: [self-hosted, zendesk-stable]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Use Node.js 20.x
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'yarn'
    
    - name: Install dependencies
      run: yarn install --frozen-lockfile
    
    - name: Set up environment variables
      run: |
        echo "NODE_ENV=test" >> $GITHUB_ENV
        echo "BRAINTRUST_PROJECT_NAME=${{ secrets.BRAINTRUST_PROJECT_NAME }}" >> $GITHUB_ENV
        echo "BRAINTRUST_API_KEY=${{ secrets.BRAINTRUST_API_KEY }}" >> $GITHUB_ENV
    
    - name: Run evaluation with Braintrust
      uses: braintrustdata/eval-action@v1
      with:
        api_key: ${{ secrets.BRAINTRUST_API_KEY }}
        runtime: node
        package_manager: npm
        paths: evals/braintrust/eval/garden.eval.ts
        terminate_on_failure: true
    
    # Clean up results directory after execution
    - name: Clean up results directory
      if: always()
      run: rm -rf evals/braintrust/results